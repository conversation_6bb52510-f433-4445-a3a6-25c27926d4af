import React from "react";
import { ScrollView } from "react-native";
import { Box } from "@/components/ui/box";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { Card } from "@/components/ui/card";
import { Progress, ProgressFilledTrack } from "@/components/ui/progress";

export default function Points() {
  const currentPoints = 1250;
  const nextRewardPoints = 1500;
  const progressPercentage = (currentPoints / nextRewardPoints) * 100;

  return (
    <ScrollView className="flex-1 bg-background-0">
      <Box className="px-6 py-8">
        <VStack space="xl">
          {/* Points Balance */}
          <VStack space="md" className="items-center">
            <Heading size="3xl" className="text-primary-600 font-bold">
              {currentPoints.toLocaleString()}
            </Heading>
            <Text size="lg" className="text-typography-700">
              Total Points
            </Text>
          </VStack>

          {/* Progress to Next Reward */}
          <Card className="p-6">
            <VStack space="md">
              <HStack className="justify-between items-center">
                <Text size="md" className="text-typography-700 font-medium">
                  Next Reward
                </Text>
                <Text size="sm" className="text-typography-500">
                  {nextRewardPoints - currentPoints} points to go
                </Text>
              </HStack>
              <Progress value={progressPercentage} className="w-full">
                <ProgressFilledTrack />
              </Progress>
              <Text size="sm" className="text-typography-500">
                Free Coffee at {nextRewardPoints} points
              </Text>
            </VStack>
          </Card>

          {/* Points Breakdown */}
          <VStack space="md">
            <Heading size="lg" className="text-typography-900">
              Points Breakdown
            </Heading>

            <Card className="p-4">
              <HStack className="justify-between items-center">
                <VStack>
                  <Text size="md" className="text-typography-700 font-medium">
                    Purchases
                  </Text>
                  <Text size="sm" className="text-typography-500">
                    1 point per $1 spent
                  </Text>
                </VStack>
                <Text size="lg" className="text-primary-600 font-bold">
                  850
                </Text>
              </HStack>
            </Card>

            <Card className="p-4">
              <HStack className="justify-between items-center">
                <VStack>
                  <Text size="md" className="text-typography-700 font-medium">
                    Bonus Points
                  </Text>
                  <Text size="sm" className="text-typography-500">
                    Special offers & promotions
                  </Text>
                </VStack>
                <Text size="lg" className="text-primary-600 font-bold">
                  400
                </Text>
              </HStack>
            </Card>
          </VStack>

          {/* Available Rewards */}
          <VStack space="md">
            <Heading size="lg" className="text-typography-900">
              Available Rewards
            </Heading>

            <Card className="p-4">
              <VStack space="sm">
                <HStack className="justify-between items-center">
                  <Text size="md" className="text-typography-700 font-medium">
                    Free Coffee
                  </Text>
                  <Text size="md" className="text-success-600 font-bold">
                    500 pts
                  </Text>
                </HStack>
                <Text size="sm" className="text-typography-500">
                  Any size, any flavor
                </Text>
              </VStack>
            </Card>

            <Card className="p-4 opacity-50">
              <VStack space="sm">
                <HStack className="justify-between items-center">
                  <Text size="md" className="text-typography-700 font-medium">
                    Free Pastry
                  </Text>
                  <Text size="md" className="text-typography-500">
                    1500 pts
                  </Text>
                </HStack>
                <Text size="sm" className="text-typography-500">
                  250 points to unlock
                </Text>
              </VStack>
            </Card>
          </VStack>
        </VStack>
      </Box>
    </ScrollView>
  );
}
