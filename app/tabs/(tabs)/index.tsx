import React from "react";
import { ScrollView } from "react-native";
import { Box } from "@/components/ui/box";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";

export default function Home() {
  const { user } = useAuth();

  return (
    <ScrollView className="flex-1 bg-background-0">
      <Box className="px-6 py-8">
        <VStack space="xl">
          {/* Welcome Section */}
          <VStack space="md">
            <Heading size="2xl" className="text-typography-900">
              Welcome Back!
            </Heading>
            <Text size="md" className="text-typography-500">
              {user?.email
                ? `Hello, ${user.email}`
                : "Ready to earn some points?"}
            </Text>
          </VStack>

          {/* Points Summary Card */}
          <Card className="p-6">
            <VStack space="md">
              <HStack className="justify-between items-center">
                <Text size="lg" className="text-typography-700 font-medium">
                  Your Points Balance
                </Text>
                <Text size="2xl" className="text-primary-600 font-bold">
                  1,250
                </Text>
              </HStack>
              <Text size="sm" className="text-typography-500">
                You&apos;ve earned 150 points this week!
              </Text>
            </VStack>
          </Card>

          {/* Quick Actions */}
          <VStack space="md">
            <Heading size="lg" className="text-typography-900">
              Quick Actions
            </Heading>
            <VStack space="sm">
              <Button className="w-full">
                <ButtonText>Scan QR Code</ButtonText>
              </Button>
              <Button variant="outline" className="w-full">
                <ButtonText>View Rewards</ButtonText>
              </Button>
            </VStack>
          </VStack>

          {/* Recent Activity */}
          <VStack space="md">
            <Heading size="lg" className="text-typography-900">
              Recent Activity
            </Heading>
            <Card className="p-4">
              <VStack space="sm">
                <HStack className="justify-between items-center">
                  <Text size="md" className="text-typography-700">
                    Coffee Purchase
                  </Text>
                  <Text size="md" className="text-success-600 font-medium">
                    +25 points
                  </Text>
                </HStack>
                <Text size="sm" className="text-typography-500">
                  2 hours ago
                </Text>
              </VStack>
            </Card>
          </VStack>
        </VStack>
      </Box>
    </ScrollView>
  );
}
