import React from 'react';
import { <PERSON><PERSON>View, Alert } from 'react-native';
import { router } from 'expo-router';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Heading } from '@/components/ui/heading';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Pressable } from '@/components/ui/pressable';
import { useAuth } from '@/contexts/AuthContext';

export default function Settings() {
  const { user, signOut } = useAuth();

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            const { error } = await signOut();
            if (!error) {
              router.replace('/(auth)/sign-in');
            }
          },
        },
      ]
    );
  };

  const settingsItems = [
    {
      title: 'Profile',
      description: 'Manage your account information',
      onPress: () => {
        // Navigate to profile screen
      },
    },
    {
      title: 'Notifications',
      description: 'Manage your notification preferences',
      onPress: () => {
        // Navigate to notifications screen
      },
    },
    {
      title: 'Privacy',
      description: 'Privacy settings and data management',
      onPress: () => {
        // Navigate to privacy screen
      },
    },
    {
      title: 'Help & Support',
      description: 'Get help and contact support',
      onPress: () => {
        // Navigate to help screen
      },
    },
    {
      title: 'About',
      description: 'App version and information',
      onPress: () => {
        // Navigate to about screen
      },
    },
  ];

  return (
    <ScrollView className="flex-1 bg-background-0">
      <Box className="px-6 py-8">
        <VStack space="xl">
          <Heading size="2xl" className="text-typography-900">
            Settings
          </Heading>

          {/* User Info Card */}
          <Card className="p-6">
            <VStack space="md">
              <Heading size="lg" className="text-typography-900">
                Account
              </Heading>
              <VStack space="sm">
                <Text size="md" className="text-typography-700 font-medium">
                  {user?.email || 'Not signed in'}
                </Text>
                <Text size="sm" className="text-typography-500">
                  Member since {new Date().getFullYear()}
                </Text>
              </VStack>
            </VStack>
          </Card>

          {/* Settings Items */}
          <VStack space="sm">
            {settingsItems.map((item, index) => (
              <Pressable key={index} onPress={item.onPress}>
                <Card className="p-4">
                  <HStack className="justify-between items-center">
                    <VStack className="flex-1">
                      <Text size="md" className="text-typography-700 font-medium">
                        {item.title}
                      </Text>
                      <Text size="sm" className="text-typography-500">
                        {item.description}
                      </Text>
                    </VStack>
                    <Text size="lg" className="text-typography-400">
                      →
                    </Text>
                  </HStack>
                </Card>
              </Pressable>
            ))}
          </VStack>

          {/* App Info */}
          <Card className="p-4">
            <VStack space="sm">
              <Text size="sm" className="text-typography-500 text-center">
                Indie Points Customer App
              </Text>
              <Text size="xs" className="text-typography-400 text-center">
                Version 1.0.0
              </Text>
            </VStack>
          </Card>

          {/* Sign Out Button */}
          <Button 
            variant="outline" 
            onPress={handleSignOut}
            className="w-full border-error-300"
          >
            <ButtonText className="text-error-600">Sign Out</ButtonText>
          </Button>
        </VStack>
      </Box>
    </ScrollView>
  );
}
