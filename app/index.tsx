import React, { useEffect } from "react";
import { router } from "expo-router";
import { Box } from "@/components/ui/box";
import { VStack } from "@/components/ui/vstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { Spinner } from "@/components/ui/spinner";
import { useAuth } from "@/contexts/AuthContext";
import Gradient from "@/assets/Icons/Gradient";
import Logo from "@/assets/Icons/Logo";

export default function SplashScreen() {
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (user) {
        // User is authenticated, redirect to main app
        router.replace("/tabs");
      } else {
        // User is not authenticated, redirect to sign in
        router.replace("/(auth)/sign-in");
      }
    }
  }, [user, loading]);

  return (
    <Box className="flex-1 bg-primary-600 justify-center items-center">
      <Box className="absolute h-[500px] w-[500px] lg:w-[700px] lg:h-[700px]">
        <Gradient />
      </Box>

      <VStack space="xl" className="items-center z-10">
        <Box className="h-[120px] w-[120px]">
          <Logo />
        </Box>

        <VStack space="md" className="items-center">
          <Heading size="3xl" className="text-white font-bold">
            Indie Points
          </Heading>
          <Text size="lg" className="text-white/80 text-center">
            Earn rewards with every purchase
          </Text>
        </VStack>

        <Spinner size="large" color="white" />
      </VStack>
    </Box>
  );
}
